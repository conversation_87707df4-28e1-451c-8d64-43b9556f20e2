#include "aimbot.hpp"
#include "../../entity.hpp"
#include "../../globals.hpp"
#include "../../gamedata.hpp"
#include <algorithm>
#include <cmath>

// ===========================
// AIMBOT IMPLEMENTATION
// ===========================

void Aimbot::doAimbot(const Reader& reader)
{
	view_matrix_t viewMatrix = GameData::getViewMatrix();
	auto playerList = reader.getPlayerListCopy();

	std::vector<Vector> playerPositions;
	playerPositions.clear();
	
	for (const auto& player : playerList)
	{
		// get the 3D position of the player we're CURRENTLY looping through.
		Vector playerPosition = driver::read_memory<Vector>(GameVars::getInstance()->getDriver(), player.BoneArray + bones::head * 32);

		
		//if (!player.enemySpotted && player.playerTeam != localTeam && Legitbot.visiblecheck)
		//	continue;

		int localTeam = GameData::getLocalTeam();
		if (player.team == localTeam && globals::Legitbot::teamcheck)
			continue;
	

		Vector h;
		if (Vector::world_to_screen(viewMatrix, playerPosition, h))
		{   
			playerPositions.push_back(h);
		}
	}

	if (GetAsyncKeyState(0x58)) // X on keyboard
	{
		auto closest_player = findClosest(playerPositions);
		if (!closest_player.IsZero())
		{
			MoveMouseToPlayer(closest_player);
		}
	}
}

Vector Aimbot::findClosest(const std::vector<Vector> playerPositions)
{
	if (playerPositions.empty()) return Vector{0,0,0};

	Vector center_of_screen{
		static_cast<float>(GetSystemMetrics(SM_CXSCREEN)) / 2,
		static_cast<float>(GetSystemMetrics(SM_CYSCREEN)) / 2,
		0.0f
	};
	

	float max_distance_sq = 5 * globals::Legitbot::radius * globals::Legitbot::radius * 5;
	float closest_distance_sq = FLT_MAX;
	Vector closest = Vector{0,0,0};

	for (const auto& pos : playerPositions) {
		float dx = pos.x - center_of_screen.x;
		float dy = pos.y - center_of_screen.y;
		float distance_sq = dx*dx + dy*dy;

		if (distance_sq < closest_distance_sq && distance_sq < max_distance_sq) {
			closest_distance_sq = distance_sq;
			closest = pos;
		}
	}
	return closest;
}

void Aimbot::MoveMouseToPlayer(Vector position)
{
	if (position.IsZero())
		return;

	POINT currentMousePos;
	GetCursorPos(&currentMousePos);
	Vector currentPos{
		static_cast<float>(currentMousePos.x),
		static_cast<float>(currentMousePos.y),
		0.0f
	};

	float deltaX = position.x - currentPos.x;
	float deltaY = position.y - currentPos.y;
	float distance = std::sqrt(deltaX * deltaX + deltaY * deltaY);

	// Deadzone - stop micro-movements that cause jitter
	const float deadzone = 0.5f;
	if (distance < deadzone) {
		return;
	}

	// Bei Smoothness 1 sofort auf den Kopf (keine Glättung)
	if (globals::Legitbot::smoothness <= 1.0f) {
		// Limit maximum movement per frame to prevent overshooting
		const float maxMovePerFrame = 50.0f;
		if (distance > maxMovePerFrame) {
			float scale = maxMovePerFrame / distance;
			deltaX *= scale;
			deltaY *= scale;
		}

		mouse_event(MOUSEEVENTF_MOVE, static_cast<LONG>(std::round(deltaX)),
			static_cast<LONG>(std::round(deltaY)), 0, 0);
		return;
	}

	// Improved smoothing algorithm - no adaptive smoothness to prevent overshooting
	const float smoothness = (globals::Legitbot::smoothness > 2.0f) ? globals::Legitbot::smoothness : 2.0f;

	// Calculate step size - smaller steps for better precision
	float stepX = deltaX / smoothness;
	float stepY = deltaY / smoothness;

	// Limit step size to prevent large jumps
	const float maxStepSize = 10.0f;
	float stepDistance = std::sqrt(stepX * stepX + stepY * stepY);
	if (stepDistance > maxStepSize) {
		float stepScale = maxStepSize / stepDistance;
		stepX *= stepScale;
		stepY *= stepScale;
	}

	// Accumulate fractional movements for precision
	accumulatedX += stepX;
	accumulatedY += stepY;

	// Convert to integer movement
	LONG moveX = static_cast<LONG>(std::round(accumulatedX));
	LONG moveY = static_cast<LONG>(std::round(accumulatedY));

	// Only move if we have at least 1 pixel movement
	if (std::abs(moveX) >= 1 || std::abs(moveY) >= 1) {
		// Subtract the movement we're about to make from accumulation
		accumulatedX -= moveX;
		accumulatedY -= moveY;

		mouse_event(MOUSEEVENTF_MOVE, moveX, moveY, 0, 0);
	}
}

// ===========================
// THREAD IMPLEMENTATION
// ===========================

void Aimbot::ThreadAimbot(const Reader& reader)
{
	// Wait for complete initialization
	while (!GameData::isInitialized()) {
		std::this_thread::sleep_for(std::chrono::milliseconds(100));
	}

	while (shouldRun.load()) {
		if (globals::Legitbot::enabled) {
			std::lock_guard<std::mutex> lock(aimbotMutex);
			doAimbot(reader);
		}

		// Reduced sleep time for better responsiveness, but not too fast to avoid performance issues
		std::this_thread::sleep_for(std::chrono::milliseconds(8));
	}
}

void Aimbot::startAimbotThread(const Reader& reader)
{
	shouldRun.store(true);
	std::thread aimbotThread(&Aimbot::ThreadAimbot, this, std::ref(reader));
	aimbotThread.detach();
}

void Aimbot::stopAimbotThread()
{
	shouldRun.store(false);
}