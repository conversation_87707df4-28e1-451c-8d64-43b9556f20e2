#pragma once
#include "../../gamedata.hpp"
#include "../../globals.hpp"
#include "../../bones.hpp"
#include "../../../render/render.hpp"
#include "../../../math/vector.hpp"
#include "../../entity.hpp"

#include <thread>
#include <chrono>
#include <Windows.h>

class Aimbot
{
public:
	void doAimbot(const Reader& reader);
private:
	Vector findClosest(const std::vector<Vector> playerPositions);
	void MoveMouseToPlayer(Vector position);
  float accumulatedX = 0.0f;
  float accumulatedY = 0.0f;
};

inline Aimbot aimbot;