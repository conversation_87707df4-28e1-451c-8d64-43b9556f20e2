#pragma once
#include "../../gamedata.hpp"
#include "../../globals.hpp"
#include "../../bones.hpp"
#include "../../../render/render.hpp"
#include "../../../math/vector.hpp"
#include "../../entity.hpp"

#include <thread>
#include <chrono>
#include <atomic>
#include <mutex>
#include <Windows.h>

class Aimbot
{
public:
	void doAimbot(const Reader& reader);
	void ThreadAimbot(const Reader& reader); // New thread function
	void startAimbotThread(const Reader& reader); // Thread starter
	void stopAimbotThread(); // Thread stopper

private:
	Vector findClosest(const std::vector<Vector> playerPositions);
	void MoveMouseToPlayer(Vector position);

	// Thread-safe members
	std::atomic<bool> shouldRun{true};
	std::mutex aimbotMutex;

	// Accumulated values for smooth movement
	float accumulatedX = 0.0f;
	float accumulatedY = 0.0f;
};

inline Aimbot aimbot;